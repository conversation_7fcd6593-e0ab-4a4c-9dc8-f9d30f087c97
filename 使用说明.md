# PDF表格识别和分页查询工具使用说明

## 🎉 成功完成！

您的PDF表格识别系统已经成功创建并运行。以下是使用方法和功能说明：

## 📁 生成的文件

- `pdf_table_extractor.py` - 核心表格提取类
- `main.py` - 主程序入口
- `config.py` - 配置文件
- `requirements.txt` - 依赖包列表
- `quick_test.py` - 快速测试脚本
- `README.md` - 详细文档
- `亚马逊表格数据.xlsx` - 已导出的Excel数据

## 🚀 快速开始

### 1. 基本命令行使用

```bash
# 提取所有表格并显示前10行
python main.py 1G3R-KQ9X-C37V.pdf

# 指定分页大小
python main.py 1G3R-KQ9X-C37V.pdf --page-size 5

# 直接导出到Excel
python main.py 1G3R-KQ9X-C37V.pdf --output "结果.xlsx" --format excel

# 指定提取方法
python main.py 1G3R-KQ9X-C37V.pdf --method pdfplumber
```

### 2. 交互式模式

```bash
python main.py 1G3R-KQ9X-C37V.pdf --interactive
```

交互式命令：
- `extract auto` - 自动提取所有表格
- `show 1 5` - 显示第1页，每页5条记录
- `search Ford` - 搜索包含"Ford"的数据
- `export excel 输出.xlsx` - 导出为Excel文件
- `summary` - 显示表格摘要
- `quit` - 退出

## 📊 识别结果

从您的PDF文件 `1G3R-KQ9X-C37V.pdf` 中成功识别出：

- **总页数**: 3页
- **总表格数**: 3个表格
- **总行数**: 6行数据

### 页面详情：
- **页面1**: 1个表格，3行x5列 - 包含透明度代码和价格信息
- **页面2**: 1个表格，1行x1列 - 包含SKU产品信息
- **页面3**: 1个表格，2行x1列 - 包含更多SKU信息

## 🔍 数据内容

识别出的主要数据包括：
- 透明度代码 (Transparency codes)
- 产品SKU编号
- 价格信息 ($0.05 per code, $234.60总计)
- 产品描述 (儿童电动车相关产品)

## 📈 分页查询功能

系统支持：
- ✅ 按页面查看数据
- ✅ 自定义每页显示条数
- ✅ 数据搜索和过滤
- ✅ 多格式导出 (Excel, CSV, JSON)

## 🛠️ 技术特性

- **多种提取方法**: pdfplumber, tabula-py, camelot
- **自动优化**: 自动选择最佳提取方法
- **错误处理**: 完善的异常处理机制
- **内存优化**: 支持大文件分页处理
- **中文支持**: 完全支持中文文件名和内容

## 📝 使用建议

1. **首次使用**: 运行 `python quick_test.py` 验证环境
2. **大文件处理**: 使用 `--pages` 参数指定页面范围
3. **数据导出**: 推荐使用Excel格式，便于后续处理
4. **搜索功能**: 在交互模式中使用search命令快速定位数据

## 🎯 下一步

您现在可以：
1. 打开 `亚马逊表格数据.xlsx` 查看完整的提取结果
2. 使用交互模式进一步探索数据
3. 根据需要调整配置参数
4. 处理其他PDF文件

## 💡 提示

- 如果提取效果不理想，可以尝试不同的方法 (pdfplumber/tabula/camelot)
- 对于复杂表格，建议使用tabula方法
- 系统会自动处理中文编码问题

---

**恭喜！您的PDF表格识别系统已经成功运行并完成了数据提取！** 🎉
