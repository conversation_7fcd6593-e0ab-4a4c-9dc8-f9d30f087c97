"""
PDF表格识别主程序
"""

import os
import sys
import argparse
from typing import Optional, List
from pdf_table_extractor import PDFTableExtractor
import pandas as pd
import json


def print_table_summary(tables_dict):
    """打印表格摘要信息"""
    total_tables = sum(len(tables) for tables in tables_dict.values())
    total_rows = sum(sum(len(df) for df in tables) for tables in tables_dict.values())
    
    print(f"\n=== 表格提取摘要 ===")
    print(f"总页数: {len(tables_dict)}")
    print(f"总表格数: {total_tables}")
    print(f"总行数: {total_rows}")
    
    for page_num, tables in tables_dict.items():
        print(f"\n页面 {page_num}: {len(tables)} 个表格")
        for i, df in enumerate(tables):
            print(f"  表格 {i+1}: {df.shape[0]} 行 x {df.shape[1]} 列")
            if not df.empty:
                print(f"    列名: {list(df.columns)}")


def print_paginated_data(paginated_result):
    """打印分页数据"""
    data = paginated_result['data']
    pagination = paginated_result['pagination']
    
    print(f"\n=== 分页数据 (第 {pagination['current_page']}/{pagination['total_pages']} 页) ===")
    print(f"显示 {len(data)} 条记录，共 {pagination['total_rows']} 条")
    
    if data:
        # 创建DataFrame用于美观显示
        df = pd.DataFrame(data)
        print(df.to_string(index=False, max_rows=20))
        
        if len(data) > 20:
            print(f"... 还有 {len(data) - 20} 行数据")
    
    print(f"\n导航: 上一页({pagination['has_prev']}) | 下一页({pagination['has_next']})")


def interactive_mode(extractor: PDFTableExtractor):
    """交互式模式"""
    print("\n=== PDF表格识别交互式模式 ===")
    print("可用命令:")
    print("  extract [method] [pages] - 提取表格 (method: auto/pdfplumber/tabula/camelot)")
    print("  show [page] [size] - 显示分页数据")
    print("  search <term> [columns] - 搜索数据")
    print("  export <format> <path> - 导出数据 (format: excel/csv/json)")
    print("  summary - 显示表格摘要")
    print("  help - 显示帮助")
    print("  quit - 退出")
    
    tables_data = {}
    
    while True:
        try:
            command = input("\n> ").strip().split()
            if not command:
                continue
                
            cmd = command[0].lower()
            
            if cmd == 'quit':
                break
            elif cmd == 'help':
                print("命令帮助:")
                print("  extract auto - 自动选择最佳方法提取所有页面")
                print("  extract pdfplumber 1,2,3 - 使用pdfplumber提取指定页面")
                print("  show 1 10 - 显示第1页，每页10条记录")
                print("  search 产品 - 在所有列中搜索'产品'")
                print("  search 产品 名称,描述 - 在指定列中搜索")
                print("  export excel output.xlsx - 导出为Excel文件")
                
            elif cmd == 'extract':
                method = command[1] if len(command) > 1 else 'auto'
                pages = None
                if len(command) > 2:
                    try:
                        pages = [int(p.strip()) for p in command[2].split(',')]
                    except ValueError:
                        print("页面格式错误，使用逗号分隔的数字，如: 1,2,3")
                        continue
                
                print(f"正在使用 {method} 方法提取表格...")
                tables_data = extractor.extract_all_tables(method, pages)
                
                if tables_data:
                    print_table_summary(tables_data)
                else:
                    print("未找到任何表格数据")
                    
            elif cmd == 'show':
                if not tables_data:
                    print("请先使用 extract 命令提取表格数据")
                    continue
                    
                page = int(command[1]) if len(command) > 1 else 1
                page_size = int(command[2]) if len(command) > 2 else 10
                
                paginated = extractor.get_paginated_data(tables_data, page, page_size)
                print_paginated_data(paginated)
                
            elif cmd == 'search':
                if not tables_data:
                    print("请先使用 extract 命令提取表格数据")
                    continue
                    
                if len(command) < 2:
                    print("请提供搜索词")
                    continue
                    
                search_term = command[1]
                columns = None
                if len(command) > 2:
                    columns = [col.strip() for col in command[2].split(',')]
                
                results = extractor.search_tables(tables_data, search_term, columns)
                if results:
                    print_table_summary(results)
                    # 显示搜索结果的前几行
                    paginated = extractor.get_paginated_data(results, 1, 5)
                    print_paginated_data(paginated)
                else:
                    print(f"未找到包含 '{search_term}' 的数据")
                    
            elif cmd == 'export':
                if not tables_data:
                    print("请先使用 extract 命令提取表格数据")
                    continue
                    
                if len(command) < 3:
                    print("请提供导出格式和文件路径")
                    continue
                    
                format_type = command[1]
                output_path = command[2]
                
                try:
                    result_path = extractor.export_tables(tables_data, output_path, format_type)
                    print(f"数据已成功导出到: {result_path}")
                except Exception as e:
                    print(f"导出失败: {e}")
                    
            elif cmd == 'summary':
                if tables_data:
                    print_table_summary(tables_data)
                else:
                    print("请先使用 extract 命令提取表格数据")
                    
            else:
                print(f"未知命令: {cmd}，输入 help 查看帮助")
                
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            break
        except Exception as e:
            print(f"执行命令时出错: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PDF表格识别和分页查询工具')
    parser.add_argument('pdf_path', help='PDF文件路径')
    parser.add_argument('--method', choices=['auto', 'pdfplumber', 'tabula', 'camelot'], 
                       default='auto', help='表格提取方法')
    parser.add_argument('--pages', help='指定页面，用逗号分隔，如: 1,2,3')
    parser.add_argument('--output', help='输出文件路径')
    parser.add_argument('--format', choices=['excel', 'csv', 'json'], 
                       default='excel', help='输出格式')
    parser.add_argument('--interactive', action='store_true', help='启动交互式模式')
    parser.add_argument('--page-size', type=int, default=10, help='分页大小')
    
    args = parser.parse_args()
    
    # 检查PDF文件是否存在
    if not os.path.exists(args.pdf_path):
        print(f"错误: PDF文件不存在 - {args.pdf_path}")
        return 1
    
    try:
        # 创建提取器
        extractor = PDFTableExtractor(args.pdf_path)
        
        if args.interactive:
            # 交互式模式
            interactive_mode(extractor)
        else:
            # 命令行模式
            pages = None
            if args.pages:
                try:
                    pages = [int(p.strip()) for p in args.pages.split(',')]
                except ValueError:
                    print("页面格式错误，使用逗号分隔的数字，如: 1,2,3")
                    return 1
            
            print(f"正在提取PDF表格: {args.pdf_path}")
            tables_data = extractor.extract_all_tables(args.method, pages)
            
            if tables_data:
                print_table_summary(tables_data)
                
                # 显示分页数据
                paginated = extractor.get_paginated_data(tables_data, 1, args.page_size)
                print_paginated_data(paginated)
                
                # 导出数据
                if args.output:
                    result_path = extractor.export_tables(tables_data, args.output, args.format)
                    print(f"\n数据已导出到: {result_path}")
            else:
                print("未找到任何表格数据")
                return 1
                
    except Exception as e:
        print(f"程序执行出错: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
