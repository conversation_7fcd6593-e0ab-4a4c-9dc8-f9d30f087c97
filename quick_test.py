"""
快速测试脚本 - 用于验证PDF文件和基本功能
"""

import os
import sys
from pdf_table_extractor import PDFTableExtractor


def quick_test():
    """快速测试PDF表格识别功能"""
    pdf_path = "1G3R-KQ9X-C37V.pdf"
    
    print("=== PDF表格识别快速测试 ===")
    
    # 检查PDF文件
    if not os.path.exists(pdf_path):
        print(f"❌ PDF文件不存在: {pdf_path}")
        return False
    
    print(f"✅ 找到PDF文件: {pdf_path}")
    
    try:
        # 创建提取器
        print("📄 正在初始化PDF提取器...")
        extractor = PDFTableExtractor(pdf_path)
        print(f"✅ PDF文件加载成功，共 {extractor.page_count} 页")
        
        # 测试第一页
        print("\n🔍 正在测试第一页表格提取...")
        
        # 尝试pdfplumber方法
        print("  测试 pdfplumber 方法...")
        try:
            tables_pdf = extractor.extract_tables_pdfplumber([0])  # 第一页
            if tables_pdf and 1 in tables_pdf and tables_pdf[1]:
                print(f"  ✅ pdfplumber: 找到 {len(tables_pdf[1])} 个表格")
                for i, df in enumerate(tables_pdf[1]):
                    print(f"    表格 {i+1}: {df.shape[0]} 行 x {df.shape[1]} 列")
            else:
                print("  ⚠️ pdfplumber: 未找到表格")
        except Exception as e:
            print(f"  ❌ pdfplumber 失败: {e}")
        
        # 尝试tabula方法
        print("  测试 tabula 方法...")
        try:
            tables_tab = extractor.extract_tables_tabula([1])  # 第一页
            if tables_tab and 1 in tables_tab and tables_tab[1]:
                print(f"  ✅ tabula: 找到 {len(tables_tab[1])} 个表格")
                for i, df in enumerate(tables_tab[1]):
                    print(f"    表格 {i+1}: {df.shape[0]} 行 x {df.shape[1]} 列")
            else:
                print("  ⚠️ tabula: 未找到表格")
        except Exception as e:
            print(f"  ❌ tabula 失败: {e}")
        
        # 尝试camelot方法
        print("  测试 camelot 方法...")
        try:
            tables_cam = extractor.extract_tables_camelot([1])  # 第一页
            if tables_cam and 1 in tables_cam and tables_cam[1]:
                print(f"  ✅ camelot: 找到 {len(tables_cam[1])} 个表格")
                for i, df in enumerate(tables_cam[1]):
                    print(f"    表格 {i+1}: {df.shape[0]} 行 x {df.shape[1]} 列")
            else:
                print("  ⚠️ camelot: 未找到表格")
        except Exception as e:
            print(f"  ❌ camelot 失败: {e}")
        
        # 自动提取测试
        print("\n🤖 测试自动提取方法...")
        try:
            tables_auto = extractor.extract_all_tables(method='auto', pages=[1])
            if tables_auto:
                total_tables = sum(len(tables) for tables in tables_auto.values())
                print(f"✅ 自动提取成功，找到 {total_tables} 个表格")
                
                # 测试分页功能
                print("\n📄 测试分页功能...")
                paginated = extractor.get_paginated_data(tables_auto, page=1, page_size=5)
                print(f"✅ 分页测试成功")
                print(f"  当前页: {paginated['pagination']['current_page']}")
                print(f"  总行数: {paginated['pagination']['total_rows']}")
                print(f"  总页数: {paginated['pagination']['total_pages']}")
                
                # 显示前几行数据
                if paginated['data']:
                    print("\n📊 前几行数据预览:")
                    for i, row in enumerate(paginated['data'][:3]):
                        print(f"  行 {i+1}: {dict(list(row.items())[:3])}...")  # 只显示前3列
                
                # 测试导出功能
                print("\n💾 测试导出功能...")
                try:
                    output_path = extractor.export_tables(tables_auto, "test_output", "json")
                    print(f"✅ 导出测试成功: {output_path}")
                    
                    # 清理测试文件
                    if os.path.exists(output_path):
                        os.remove(output_path)
                        print("🧹 清理测试文件完成")
                except Exception as e:
                    print(f"❌ 导出测试失败: {e}")
                
            else:
                print("⚠️ 自动提取未找到表格")
                
        except Exception as e:
            print(f"❌ 自动提取失败: {e}")
        
        print("\n🎉 快速测试完成！")
        print("\n💡 使用建议:")
        print("  1. 运行完整提取: python main.py 1G3R-KQ9X-C37V.pdf")
        print("  2. 交互式模式: python main.py 1G3R-KQ9X-C37V.pdf --interactive")
        print("  3. 指定方法: python main.py 1G3R-KQ9X-C37V.pdf --method pdfplumber")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False


if __name__ == "__main__":
    success = quick_test()
    sys.exit(0 if success else 1)
