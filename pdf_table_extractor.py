"""
PDF表格识别和分页查询核心模块
"""

import logging
import pandas as pd
import pdfplumber
import tabula
import camelot
import json
import os
from typing import List, Dict, Any, Optional, Tuple
from config import EXTRACTION_METHODS, PAGINATION_CONFIG, TABLE_DETECTION_PARAMS

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PDFTableExtractor:
    """PDF表格提取器"""
    
    def __init__(self, pdf_path: str):
        """
        初始化PDF表格提取器
        
        Args:
            pdf_path: PDF文件路径
        """
        self.pdf_path = pdf_path
        self.tables_cache = {}
        self.page_count = 0
        self._validate_pdf()
        
    def _validate_pdf(self):
        """验证PDF文件"""
        if not os.path.exists(self.pdf_path):
            raise FileNotFoundError(f"PDF文件不存在: {self.pdf_path}")
        
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                self.page_count = len(pdf.pages)
                logger.info(f"PDF文件加载成功，共 {self.page_count} 页")
        except Exception as e:
            raise ValueError(f"无法打开PDF文件: {e}")
    
    def extract_tables_pdfplumber(self, pages: Optional[List[int]] = None) -> Dict[int, List[pd.DataFrame]]:
        """
        使用pdfplumber提取表格
        
        Args:
            pages: 指定页面列表，None表示所有页面
            
        Returns:
            字典，键为页码，值为该页的表格列表
        """
        tables_by_page = {}
        
        try:
            with pdfplumber.open(self.pdf_path) as pdf:
                target_pages = pages if pages else range(len(pdf.pages))
                
                for page_num in target_pages:
                    if page_num >= len(pdf.pages):
                        logger.warning(f"页面 {page_num + 1} 超出范围")
                        continue
                        
                    page = pdf.pages[page_num]
                    tables = page.extract_tables(
                        table_settings=TABLE_DETECTION_PARAMS['pdfplumber']['table_settings']
                    )
                    
                    page_tables = []
                    for i, table in enumerate(tables):
                        if table and len(table) > 1:  # 确保表格有数据
                            df = pd.DataFrame(table[1:], columns=table[0])
                            df = df.dropna(how='all').dropna(axis=1, how='all')  # 清理空行空列
                            if not df.empty:
                                page_tables.append(df)
                                logger.info(f"页面 {page_num + 1} 提取到表格 {i + 1}，大小: {df.shape}")
                    
                    tables_by_page[page_num + 1] = page_tables
                    
        except Exception as e:
            logger.error(f"pdfplumber提取失败: {e}")
            
        return tables_by_page
    
    def extract_tables_tabula(self, pages: Optional[List[int]] = None) -> Dict[int, List[pd.DataFrame]]:
        """
        使用tabula-py提取表格
        
        Args:
            pages: 指定页面列表，None表示所有页面
            
        Returns:
            字典，键为页码，值为该页的表格列表
        """
        tables_by_page = {}
        
        try:
            target_pages = pages if pages else list(range(1, self.page_count + 1))
            
            for page_num in target_pages:
                if page_num > self.page_count:
                    logger.warning(f"页面 {page_num} 超出范围")
                    continue
                    
                tables = tabula.read_pdf(
                    self.pdf_path,
                    pages=page_num,
                    **TABLE_DETECTION_PARAMS['tabula']
                )
                
                page_tables = []
                for i, df in enumerate(tables):
                    if not df.empty:
                        df = df.dropna(how='all').dropna(axis=1, how='all')
                        if not df.empty:
                            page_tables.append(df)
                            logger.info(f"页面 {page_num} 提取到表格 {i + 1}，大小: {df.shape}")
                
                tables_by_page[page_num] = page_tables
                
        except Exception as e:
            logger.error(f"tabula提取失败: {e}")
            
        return tables_by_page
    
    def extract_tables_camelot(self, pages: Optional[List[int]] = None) -> Dict[int, List[pd.DataFrame]]:
        """
        使用camelot提取表格
        
        Args:
            pages: 指定页面列表，None表示所有页面
            
        Returns:
            字典，键为页码，值为该页的表格列表
        """
        tables_by_page = {}
        
        try:
            if pages:
                pages_str = ','.join(map(str, pages))
            else:
                pages_str = 'all'
                
            tables = camelot.read_pdf(
                self.pdf_path,
                pages=pages_str,
                **TABLE_DETECTION_PARAMS['camelot']
            )
            
            for table in tables:
                page_num = table.page
                df = table.df
                
                if not df.empty:
                    df = df.dropna(how='all').dropna(axis=1, how='all')
                    if not df.empty:
                        if page_num not in tables_by_page:
                            tables_by_page[page_num] = []
                        tables_by_page[page_num].append(df)
                        logger.info(f"页面 {page_num} 提取到表格，大小: {df.shape}")
                        
        except Exception as e:
            logger.error(f"camelot提取失败: {e}")
            
        return tables_by_page
    
    def extract_all_tables(self, method: str = 'auto', pages: Optional[List[int]] = None) -> Dict[int, List[pd.DataFrame]]:
        """
        提取所有表格
        
        Args:
            method: 提取方法 ('pdfplumber', 'tabula', 'camelot', 'auto')
            pages: 指定页面列表
            
        Returns:
            字典，键为页码，值为该页的表格列表
        """
        if method == 'auto':
            # 自动选择最佳方法
            for method_name in ['pdfplumber', 'tabula', 'camelot']:
                try:
                    result = getattr(self, f'extract_tables_{method_name}')(pages)
                    if result:
                        logger.info(f"使用 {method_name} 成功提取表格")
                        return result
                except Exception as e:
                    logger.warning(f"{method_name} 提取失败: {e}")
                    continue
            
            logger.error("所有提取方法都失败了")
            return {}
        else:
            return getattr(self, f'extract_tables_{method}')(pages)

    def get_paginated_data(self, tables_dict: Dict[int, List[pd.DataFrame]],
                          page: int = 1, page_size: int = None) -> Dict[str, Any]:
        """
        获取分页数据

        Args:
            tables_dict: 表格字典
            page: 页码（从1开始）
            page_size: 每页大小

        Returns:
            分页结果字典
        """
        if page_size is None:
            page_size = PAGINATION_CONFIG['default_page_size']

        page_size = min(page_size, PAGINATION_CONFIG['max_page_size'])

        # 合并所有表格数据
        all_data = []
        for page_num, tables in tables_dict.items():
            for table_idx, df in enumerate(tables):
                for row_idx, row in df.iterrows():
                    row_data = row.to_dict()
                    row_data['_source_page'] = page_num
                    row_data['_table_index'] = table_idx
                    row_data['_row_index'] = row_idx
                    all_data.append(row_data)

        total_rows = len(all_data)
        total_pages = (total_rows + page_size - 1) // page_size

        start_idx = (page - 1) * page_size
        end_idx = min(start_idx + page_size, total_rows)

        paginated_data = all_data[start_idx:end_idx]

        return {
            'data': paginated_data,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_rows': total_rows,
                'total_pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        }

    def search_tables(self, tables_dict: Dict[int, List[pd.DataFrame]],
                     search_term: str, columns: Optional[List[str]] = None) -> Dict[int, List[pd.DataFrame]]:
        """
        在表格中搜索数据

        Args:
            tables_dict: 表格字典
            search_term: 搜索词
            columns: 指定搜索的列，None表示搜索所有列

        Returns:
            包含搜索结果的表格字典
        """
        result_dict = {}

        for page_num, tables in tables_dict.items():
            page_results = []

            for df in tables:
                if columns:
                    search_columns = [col for col in columns if col in df.columns]
                else:
                    search_columns = df.columns

                mask = pd.Series([False] * len(df))
                for col in search_columns:
                    mask |= df[col].astype(str).str.contains(search_term, case=False, na=False)

                filtered_df = df[mask]
                if not filtered_df.empty:
                    page_results.append(filtered_df)

            if page_results:
                result_dict[page_num] = page_results

        return result_dict

    def export_tables(self, tables_dict: Dict[int, List[pd.DataFrame]],
                     output_path: str, format: str = 'excel') -> str:
        """
        导出表格数据

        Args:
            tables_dict: 表格字典
            output_path: 输出路径
            format: 输出格式 ('excel', 'csv', 'json')

        Returns:
            导出文件路径
        """
        if format == 'excel':
            return self._export_to_excel(tables_dict, output_path)
        elif format == 'csv':
            return self._export_to_csv(tables_dict, output_path)
        elif format == 'json':
            return self._export_to_json(tables_dict, output_path)
        else:
            raise ValueError(f"不支持的格式: {format}")

    def _export_to_excel(self, tables_dict: Dict[int, List[pd.DataFrame]], output_path: str) -> str:
        """导出到Excel"""
        if not output_path.endswith('.xlsx'):
            output_path += '.xlsx'

        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            for page_num, tables in tables_dict.items():
                for table_idx, df in enumerate(tables):
                    sheet_name = f'Page{page_num}_Table{table_idx + 1}'
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

        logger.info(f"数据已导出到: {output_path}")
        return output_path

    def _export_to_csv(self, tables_dict: Dict[int, List[pd.DataFrame]], output_path: str) -> str:
        """导出到CSV"""
        if not output_path.endswith('.csv'):
            output_path += '.csv'

        all_data = []
        for page_num, tables in tables_dict.items():
            for table_idx, df in enumerate(tables):
                df_copy = df.copy()
                df_copy['_source_page'] = page_num
                df_copy['_table_index'] = table_idx
                all_data.append(df_copy)

        if all_data:
            combined_df = pd.concat(all_data, ignore_index=True)
            combined_df.to_csv(output_path, index=False, encoding='utf-8-sig')

        logger.info(f"数据已导出到: {output_path}")
        return output_path

    def _export_to_json(self, tables_dict: Dict[int, List[pd.DataFrame]], output_path: str) -> str:
        """导出到JSON"""
        if not output_path.endswith('.json'):
            output_path += '.json'

        json_data = {}
        for page_num, tables in tables_dict.items():
            json_data[f'page_{page_num}'] = []
            for table_idx, df in enumerate(tables):
                table_data = {
                    'table_index': table_idx,
                    'data': df.to_dict('records')
                }
                json_data[f'page_{page_num}'].append(table_data)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)

        logger.info(f"数据已导出到: {output_path}")
        return output_path
