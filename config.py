"""
PDF表格识别配置文件
"""

# 支持的PDF表格提取方法
EXTRACTION_METHODS = {
    'pdfplumber': {
        'name': 'PDFPlumber',
        'description': '适用于大多数文本型PDF表格',
        'priority': 1
    },
    'tabula': {
        'name': 'Tabula-py',
        'description': '适用于复杂表格结构',
        'priority': 2
    },
    'camelot': {
        'name': 'Camelot',
        'description': '适用于高质量表格提取',
        'priority': 3
    }
}

# 分页查询配置
PAGINATION_CONFIG = {
    'default_page_size': 10,  # 默认每页显示行数
    'max_page_size': 100,     # 最大每页显示行数
    'cache_enabled': True,    # 是否启用缓存
}

# 输出格式配置
OUTPUT_FORMATS = ['csv', 'excel', 'json']

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'filename': 'pdf_extraction.log'
}

# 表格检测参数
TABLE_DETECTION_PARAMS = {
    'pdfplumber': {
        'table_settings': {
            'vertical_strategy': 'lines',
            'horizontal_strategy': 'lines',
            'snap_tolerance': 3,
            'join_tolerance': 3,
            'edge_min_length': 3,
            'min_words_vertical': 3,
            'min_words_horizontal': 1,
        }
    },
    'tabula': {
        'lattice': True,
        'stream': False,
        'guess': True,
        'pandas_options': {'header': 0}
    },
    'camelot': {
        'flavor': 'lattice',
        'table_areas': None,
        'columns': None,
        'split_text': False,
        'flag_size': False,
        'strip_text': '\n'
    }
}
