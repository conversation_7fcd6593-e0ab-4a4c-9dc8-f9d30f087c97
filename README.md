# PDF表格识别和分页查询工具

这是一个功能强大的PDF表格识别工具，支持多种提取方法和分页查询功能。

## 功能特性

- 🔍 **多种提取方法**: 支持 pdfplumber、tabula-py、camelot 三种表格提取库
- 📄 **分页查询**: 支持大文件的分页处理和查询
- 🔎 **数据搜索**: 在提取的表格中搜索特定内容
- 📊 **多格式导出**: 支持导出为 Excel、CSV、JSON 格式
- 🖥️ **交互式界面**: 提供命令行交互模式
- ⚡ **自动优化**: 自动选择最佳的表格提取方法

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 命令行模式

#### 基本用法
```bash
# 自动提取所有表格并显示前10行
python main.py "1G3R-KQ9X-C37V.pdf"

# 指定提取方法
python main.py "1G3R-KQ9X-C37V.pdf" --method pdfplumber

# 指定页面范围
python main.py "1G3R-KQ9X-C37V.pdf" --pages "1,2,3"

# 设置分页大小
python main.py "1G3R-KQ9X-C37V.pdf" --page-size 20

# 直接导出结果
python main.py "1G3R-KQ9X-C37V.pdf" --output "result.xlsx" --format excel
```

### 2. 交互式模式

```bash
python main.py "1G3R-KQ9X-C37V.pdf" --interactive
```

交互式模式支持以下命令：

- `extract auto` - 自动提取所有表格
- `extract pdfplumber 1,2,3` - 使用指定方法提取特定页面
- `show 1 10` - 显示第1页数据，每页10条
- `search 产品` - 搜索包含"产品"的数据
- `search 产品 名称,描述` - 在指定列中搜索
- `export excel output.xlsx` - 导出为Excel文件
- `summary` - 显示表格摘要
- `help` - 显示帮助信息
- `quit` - 退出程序

### 3. 编程接口

```python
from pdf_table_extractor import PDFTableExtractor

# 创建提取器
extractor = PDFTableExtractor("1G3R-KQ9X-C37V.pdf")

# 提取所有表格
tables = extractor.extract_all_tables(method='auto')

# 分页查询
paginated_data = extractor.get_paginated_data(tables, page=1, page_size=10)

# 搜索数据
search_results = extractor.search_tables(tables, "关键词")

# 导出数据
extractor.export_tables(tables, "output.xlsx", "excel")
```

## 提取方法说明

### PDFPlumber
- **适用场景**: 大多数文本型PDF表格
- **优点**: 速度快，准确率高
- **缺点**: 对复杂表格支持有限

### Tabula-py
- **适用场景**: 复杂表格结构
- **优点**: 支持复杂表格，功能强大
- **缺点**: 需要Java环境

### Camelot
- **适用场景**: 高质量表格提取
- **优点**: 提取质量最高，支持表格质量评估
- **缺点**: 依赖较多，安装复杂

## 配置说明

可以通过修改 `config.py` 文件来调整各种参数：

- 表格检测参数
- 分页配置
- 输出格式设置
- 日志配置

## 常见问题

### 1. 安装依赖失败
确保已安装以下系统依赖：
- Java (用于 tabula-py)
- Ghostscript (用于 camelot)
- OpenCV (用于图像处理)

### 2. 表格提取不准确
尝试不同的提取方法：
1. 首先尝试 `pdfplumber`
2. 如果效果不好，尝试 `tabula`
3. 最后尝试 `camelot`

### 3. 内存不足
对于大文件，使用分页处理：
- 指定页面范围: `--pages "1,2,3"`
- 减小分页大小: `--page-size 5`

## 示例输出

```
=== 表格提取摘要 ===
总页数: 3
总表格数: 5
总行数: 150

页面 1: 2 个表格
  表格 1: 25 行 x 4 列
    列名: ['产品名称', '价格', '数量', '总计']
  表格 2: 15 行 x 3 列
    列名: ['日期', '描述', '金额']

=== 分页数据 (第 1/15 页) ===
显示 10 条记录，共 150 条
   产品名称    价格  数量    总计  _source_page  _table_index  _row_index
0    产品A  100.0   5   500.0             1             0           0
1    产品B  200.0   3   600.0             1             0           1
...
```

## 许可证

MIT License
